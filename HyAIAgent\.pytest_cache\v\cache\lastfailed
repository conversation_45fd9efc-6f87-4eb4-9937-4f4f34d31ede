{"test/test_text_analyzer.py::TestTextAnalyzer::test_file_analysis": true, "test/test_text_analyzer.py::TestTextAnalyzer::test_batch_analysis": true, "test/test_document_processor.py::TestDocumentProcessor::test_process_text_document": true, "test/test_document_processor.py::TestDocumentProcessor::test_process_json_document": true, "test/test_document_processor.py::TestDocumentProcessor::test_process_csv_document": true, "test/test_document_processor.py::TestDocumentProcessor::test_process_xml_document": true, "test/test_document_processor.py::TestDocumentProcessor::test_process_ini_document": true, "test/test_document_processor.py::TestDocumentProcessor::test_batch_process_documents": true, "test/test_document_processor.py::TestDocumentProcessor::test_search_in_documents": true, "test/test_document_processor.py::TestDocumentProcessor::test_process_invalid_json": true, "test/test_document_processor.py::TestDocumentProcessor::test_document_metadata_extraction": true, "test/test_document_processor.py::TestDocumentProcessor::test_processing_stats": true, "test/test_document_processor.py::TestDocumentProcessor::test_concurrent_processing": true, "test/test_task_integration.py": true, "test/test_task_integration.py::TestFileOperationTaskExecutor::test_file_read_task": true}